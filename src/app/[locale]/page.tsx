
import { getLandingPage } from "@/app/actions";
import { Footer } from "@/components/ui/footer-section";
import GoogleOneTapWrapper from "@/components/GoogleOneTapWrapper";
import Header from "@/components/Header";
import HeroSection from "@/components/sections/HeroSection";
import FeaturesSection from "@/components/sections/FeaturesSection";
import HowItWorksSection from "@/components/sections/HowItWorksSection";
import GallerySection from "@/components/sections/GallerySection";
import { PricingSection } from "@/components/blocks/pricing-section";
import { TestimonialsSection } from "@/components/blocks/testimonials-with-marquee";
import FAQSection from "@/components/sections/FAQSection";
import AppSection from "@/components/sections/AppSection";

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  // 使用 await 获取 locale
  const { locale } = await params;

  // 获取页面数据
  const page = await getLandingPage(locale);

  return (
    <>
      {/* Google One Tap组件 */}
      <GoogleOneTapWrapper />

      {/* Header */}
      <Header header={page.header} />

      {/* Main Content */}
      <main className="pt-16">
        {/* Hero Section */}
        <HeroSection hero={page.hero} stats={page.stats} />

        {/* Features Section */}
        <FeaturesSection introduce={page.introduce} benefit={page.benefit} />

        {/* How It Works Section */}
        <HowItWorksSection usage={page.usage} />

        {/* Gallery Section */}
        <GallerySection showcase={page.showcase} />

        {/* App Section - AI Wedding Photo Generator */}
        <AppSection app={page.app} />

        {/* Pricing Section */}
        <section id="pricing" className="relative flex items-center justify-center min-h-[calc(100vh-4rem)] w-full py-12 md:py-24 lg:py-32 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <PricingSection
              title={page.pricing.title}
              subtitle={page.pricing.subtitle}
              tiers={page.pricing.plans.map((plan: any) => ({
                name: plan.name,
                price: { monthly: plan.amount || plan.price },
                description: plan.description,
                features: plan.features,
                cta: page.pricing.getStarted,
                highlighted: plan.name === "Pro",
                popular: plan.name === "Pro"
              }))}
              frequencies={["monthly"]}
            />
          </div>
        </section>

        {/* Testimonials Section */}
        <TestimonialsSection
          title={page.testimonial.title}
          description={page.testimonial.subtitle}
          testimonials={page.testimonial.testimonials.map((testimonial: any) => ({
            author: {
              name: testimonial.author.name,
              handle: testimonial.author.title,
              avatar: testimonial.author.image
            },
            text: testimonial.content
          }))}
        />

        {/* FAQ Section */}
        <FAQSection faq={page.faq} />
      </main>

      {/* Footer */}
      <Footer />
    </>
  );
}
