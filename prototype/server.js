#!/usr/bin/env node
/**
 * Simple HTTP server for testing the AI Wedding Dress Studio locally.
 * Run with: node server.js
 * Then open http://localhost:8000 in your browser.
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

const PORT = 8000;

// MIME types for different file extensions
const mimeTypes = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'text/javascript',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon'
};

const server = http.createServer((req, res) => {
    // Parse the URL
    let filePath = '.' + req.url;
    if (filePath === './') {
        filePath = './demo.html';
    }

    // Get the file extension
    const extname = String(path.extname(filePath)).toLowerCase();
    const mimeType = mimeTypes[extname] || 'application/octet-stream';

    // Read and serve the file
    fs.readFile(filePath, (error, content) => {
        if (error) {
            if (error.code === 'ENOENT') {
                // File not found
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end(`
                    <html>
                        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                            <h1>404 - File Not Found</h1>
                            <p>The requested file could not be found.</p>
                            <a href="/demo.html">Go to Demo Page</a> | 
                            <a href="/index.html">Go to Main App</a>
                        </body>
                    </html>
                `);
            } else {
                // Server error
                res.writeHead(500);
                res.end(`Server Error: ${error.code}`);
            }
        } else {
            // Success
            res.writeHead(200, { 
                'Content-Type': mimeType,
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type'
            });
            res.end(content, 'utf-8');
        }
    });
});

server.listen(PORT, () => {
    console.log('🚀 AI Wedding Dress Studio Server');
    console.log(`📍 Serving at http://localhost:${PORT}`);
    console.log(`📱 Demo page: http://localhost:${PORT}/demo.html`);
    console.log(`🎭 Main app: http://localhost:${PORT}/index.html`);
    console.log('⏹️  Press Ctrl+C to stop the server');
    console.log('-'.repeat(50));

    // Try to open the browser automatically
    const start = (process.platform === 'darwin' ? 'open' : 
                   process.platform === 'win32' ? 'start' : 'xdg-open');
    
    exec(`${start} http://localhost:${PORT}/demo.html`, (error) => {
        if (error) {
            console.log('💡 Please open http://localhost:${PORT}/demo.html in your browser');
        } else {
            console.log('🌐 Opening browser automatically...');
        }
    });
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n👋 Server stopped. Thanks for using AI Wedding Dress Studio!');
    process.exit(0);
});