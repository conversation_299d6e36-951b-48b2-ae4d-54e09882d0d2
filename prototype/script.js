// Global variables
let uploadedFile = null;
let selectedStyle = null;
let selectedScene = null;
let generatedImages = [];

// DOM elements
const uploadArea = document.getElementById('uploadArea');
const photoInput = document.getElementById('photoInput');
const uploadedImage = document.getElementById('uploadedImage');
const previewImage = document.getElementById('previewImage');
const removeImage = document.getElementById('removeImage');
const uploadSection = document.getElementById('uploadSection');
const styleSection = document.getElementById('styleSection');
const sceneSection = document.getElementById('sceneSection');
const controlsSection = document.getElementById('controlsSection');
const loadingSection = document.getElementById('loadingSection');
const resultsSection = document.getElementById('resultsSection');
const generateBtn = document.getElementById('generateBtn');
const loadingText = document.getElementById('loadingText');
const progressFill = document.getElementById('progressFill');
const resultsGrid = document.getElementById('resultsGrid');
const imageModal = document.getElementById('imageModal');
const modalImage = document.getElementById('modalImage');
const closeModal = document.getElementById('closeModal');
const downloadSingle = document.getElementById('downloadSingle');
const generateMoreBtn = document.getElementById('generateMoreBtn');
const downloadAllBtn = document.getElementById('downloadAllBtn');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    initializeNavigation();
    initializeFAQ();
    initializeScrollEffects();
});

function initializeEventListeners() {
    // Upload functionality
    uploadArea.addEventListener('click', () => photoInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('drop', handleDrop);
    photoInput.addEventListener('change', handleFileSelect);
    removeImage.addEventListener('click', removeUploadedImage);

    // Style selection
    document.querySelectorAll('.style-option').forEach(option => {
        option.addEventListener('click', () => selectStyle(option));
    });

    // Scene selection
    document.querySelectorAll('.scene-option').forEach(option => {
        option.addEventListener('click', () => selectScene(option));
    });

    // Generation
    generateBtn.addEventListener('click', generateWeddingPortrait);

    // Modal functionality
    closeModal.addEventListener('click', closeImageModal);
    imageModal.addEventListener('click', (e) => {
        if (e.target === imageModal) closeImageModal();
    });

    // Results actions
    generateMoreBtn.addEventListener('click', generateMoreVariations);
    downloadAllBtn.addEventListener('click', downloadAllImages);
    downloadSingle.addEventListener('click', downloadCurrentImage);

    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
}

// File upload handlers
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('drag-over');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('drag-over');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        handleFile(file);
    }
}

function handleFile(file) {
    // Validate file type
    if (!file.type.startsWith('image/')) {
        showNotification('Please select a valid image file.', 'error');
        return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
        showNotification('File size must be less than 10MB.', 'error');
        return;
    }

    uploadedFile = file;
    
    // Create preview
    const reader = new FileReader();
    reader.onload = function(e) {
        previewImage.src = e.target.result;
        uploadedImage.style.display = 'block';
        uploadArea.querySelector('.upload-placeholder').style.display = 'none';
        
        // Show next section
        setTimeout(() => {
            showSection(styleSection);
        }, 500);
    };
    reader.readAsDataURL(file);
}

function removeUploadedImage() {
    uploadedFile = null;
    uploadedImage.style.display = 'none';
    uploadArea.querySelector('.upload-placeholder').style.display = 'block';
    previewImage.src = '';
    photoInput.value = '';
    
    // Hide subsequent sections
    hideSection(styleSection);
    hideSection(sceneSection);
    hideSection(controlsSection);
    hideSection(resultsSection);
    
    // Reset selections
    selectedStyle = null;
    selectedScene = null;
    document.querySelectorAll('.style-option.selected').forEach(el => {
        el.classList.remove('selected');
    });
    document.querySelectorAll('.scene-option.selected').forEach(el => {
        el.classList.remove('selected');
    });
}

// Style selection
function selectStyle(option) {
    // Remove previous selection
    document.querySelectorAll('.style-option.selected').forEach(el => {
        el.classList.remove('selected');
    });
    
    // Add selection to clicked option
    option.classList.add('selected');
    selectedStyle = option.dataset.style;
    
    // Show next section
    setTimeout(() => {
        showSection(sceneSection);
    }, 300);
}

// Scene selection
function selectScene(option) {
    // Remove previous selection
    document.querySelectorAll('.scene-option.selected').forEach(el => {
        el.classList.remove('selected');
    });
    
    // Add selection to clicked option
    option.classList.add('selected');
    selectedScene = option.dataset.scene;
    
    // Show next section
    setTimeout(() => {
        showSection(controlsSection);
    }, 300);
}

// Section visibility management
function showSection(section) {
    section.style.display = 'block';
    section.classList.add('fade-in');
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });
}

function hideSection(section) {
    section.style.display = 'none';
    section.classList.remove('fade-in');
}

// Wedding portrait generation
async function generateWeddingPortrait() {
    if (!uploadedFile || !selectedStyle || !selectedScene) {
        showNotification('Please complete all steps before generating.', 'warning');
        return;
    }

    // Get control values
    const lighting = document.getElementById('lighting').value;
    const pose = document.getElementById('pose').value;
    const quality = document.getElementById('quality').value;

    // Show loading section
    hideSection(controlsSection);
    showSection(loadingSection);

    // Simulate AI generation process
    await simulateAIGeneration();

    // Show results
    hideSection(loadingSection);
    showSection(resultsSection);
}

async function simulateAIGeneration() {
    const steps = [
        'Analyzing your photo...',
        'Applying wedding dress style...',
        'Setting up the scene...',
        'Adjusting lighting and pose...',
        'Enhancing image quality...',
        'Finalizing your portrait...'
    ];

    for (let i = 0; i < steps.length; i++) {
        loadingText.textContent = steps[i];
        progressFill.style.width = `${((i + 1) / steps.length) * 100}%`;
        await sleep(1000 + Math.random() * 1000); // Random delay between 1-2 seconds
    }

    // Generate mock results
    generateMockResults();
}

function generateMockResults() {
    // Clear previous results
    resultsGrid.innerHTML = '';
    generatedImages = [];

    // Mock generated images (in a real app, these would come from the AI service)
    const mockImages = [
        'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&h=600&fit=crop',
        'https://images.unsplash.com/photo-1515934751635-c81c6bc9a2d8?w=400&h=600&fit=crop',
        'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=400&h=600&fit=crop',
        'https://images.unsplash.com/photo-1566174053879-31528523f8ae?w=400&h=600&fit=crop'
    ];

    mockImages.forEach((imageUrl, index) => {
        const resultItem = createResultItem(imageUrl, index);
        resultsGrid.appendChild(resultItem);
        generatedImages.push(imageUrl);
    });
}

function createResultItem(imageUrl, index) {
    const resultItem = document.createElement('div');
    resultItem.className = 'result-item';
    resultItem.innerHTML = `
        <img src="${imageUrl}" alt="Generated Wedding Portrait ${index + 1}">
        <div class="result-overlay">
            <i class="fas fa-search-plus"></i>
        </div>
    `;
    
    resultItem.addEventListener('click', () => openImageModal(imageUrl));
    return resultItem;
}

// Modal functionality
function openImageModal(imageUrl) {
    modalImage.src = imageUrl;
    imageModal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closeImageModal() {
    imageModal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Download functionality
function downloadCurrentImage() {
    const imageUrl = modalImage.src;
    downloadImage(imageUrl, 'wedding-portrait.jpg');
}

function downloadAllImages() {
    generatedImages.forEach((imageUrl, index) => {
        setTimeout(() => {
            downloadImage(imageUrl, `wedding-portrait-${index + 1}.jpg`);
        }, index * 500); // Stagger downloads
    });
    showNotification('Downloading all images...', 'success');
}

function downloadImage(url, filename) {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Generate more variations
async function generateMoreVariations() {
    generateMoreBtn.classList.add('loading');
    generateMoreBtn.textContent = 'Generating...';
    
    await sleep(2000); // Simulate generation time
    
    // Add more mock results
    const additionalMockImages = [
        'https://images.unsplash.com/photo-1583939003579-730e3918a45a?w=400&h=600&fit=crop',
        'https://images.unsplash.com/photo-1595476108010-b4d1f102b1b1?w=400&h=600&fit=crop'
    ];
    
    additionalMockImages.forEach((imageUrl, index) => {
        const resultItem = createResultItem(imageUrl, generatedImages.length + index);
        resultsGrid.appendChild(resultItem);
        generatedImages.push(imageUrl);
    });
    
    generateMoreBtn.classList.remove('loading');
    generateMoreBtn.innerHTML = '<i class="fas fa-plus"></i> Generate More Variations';
    showNotification('New variations generated!', 'success');
}

// Keyboard shortcuts
function handleKeyboardShortcuts(e) {
    if (e.key === 'Escape' && imageModal.style.display === 'flex') {
        closeImageModal();
    }
    
    if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
            case 'u':
                e.preventDefault();
                photoInput.click();
                break;
            case 'Enter':
                e.preventDefault();
                if (generateBtn.style.display !== 'none') {
                    generateWeddingPortrait();
                }
                break;
        }
    }
}

// Utility functions
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)}"></i>
        <span>${message}</span>
    `;
    
    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        background: getNotificationColor(type),
        color: 'white',
        padding: '15px 20px',
        borderRadius: '10px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.2)',
        zIndex: '9999',
        display: 'flex',
        alignItems: 'center',
        gap: '10px',
        fontSize: '14px',
        fontWeight: '500',
        maxWidth: '300px',
        animation: 'slideInRight 0.3s ease-out'
    });
    
    // Add to document
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        default: return 'info-circle';
    }
}

function getNotificationColor(type) {
    switch (type) {
        case 'success': return '#2ecc71';
        case 'error': return '#e74c3c';
        case 'warning': return '#f39c12';
        default: return '#3498db';
    }
}

// Add notification animations to CSS
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(notificationStyles);

// AI Integration placeholder functions
// These would be replaced with actual AI service calls in a production app

async function callAIService(imageFile, style, scene, options) {
    // This is where you would integrate with actual AI services like:
    // - OpenAI DALL-E
    // - Midjourney API
    // - Stable Diffusion
    // - Custom trained models
    
    const formData = new FormData();
    formData.append('image', imageFile);
    formData.append('style', style);
    formData.append('scene', scene);
    formData.append('options', JSON.stringify(options));
    
    // Example API call structure:
    /*
    try {
        const response = await fetch('/api/generate-wedding-portrait', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error('AI generation failed');
        }
        
        const result = await response.json();
        return result.images;
    } catch (error) {
        console.error('AI service error:', error);
        throw error;
    }
    */
    
    // For now, return mock data
    return [
        'generated-image-1.jpg',
        'generated-image-2.jpg',
        'generated-image-3.jpg',
        'generated-image-4.jpg'
    ];
}

// Performance optimization
function optimizeImage(file, maxWidth = 1024, quality = 0.8) {
    return new Promise((resolve) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        img.onload = function() {
            const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
            canvas.width = img.width * ratio;
            canvas.height = img.height * ratio;
            
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            
            canvas.toBlob(resolve, 'image/jpeg', quality);
        };
        
        img.src = URL.createObjectURL(file);
    });
}

// Analytics and tracking (placeholder)
function trackEvent(eventName, properties = {}) {
    // This would integrate with analytics services like:
    // - Google Analytics
    // - Mixpanel
    // - Amplitude
    
    console.log('Event tracked:', eventName, properties);
    
    // Example implementation:
    /*
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, properties);
    }
    
    if (typeof mixpanel !== 'undefined') {
        mixpanel.track(eventName, properties);
    }
    */
}

// Error handling and logging
window.addEventListener('error', function(e) {
    console.error('Application error:', e.error);
    showNotification('An unexpected error occurred. Please try again.', 'error');
    
    // In production, you would send this to an error tracking service
    // like Sentry, Rollbar, or Bugsnag
});

// Service worker registration for PWA capabilities
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
            })
            .catch(function(err) {
                console.log('ServiceWorker registration failed');
            });
    });
}

// Navigation functionality
function initializeNavigation() {
    const navToggle = document.getElementById('navToggle');
    const navMenu = document.getElementById('navMenu');
    const navLinks = document.querySelectorAll('.nav-link');
    
    // Mobile menu toggle
    if (navToggle) {
        navToggle.addEventListener('click', function() {
            navToggle.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }
    
    // Close mobile menu when clicking on a link
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            navToggle.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });
    
    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 70; // Account for fixed navbar
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// FAQ functionality
function initializeFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        
        question.addEventListener('click', function() {
            const isActive = item.classList.contains('active');
            
            // Close all FAQ items
            faqItems.forEach(faqItem => {
                faqItem.classList.remove('active');
            });
            
            // Open clicked item if it wasn't active
            if (!isActive) {
                item.classList.add('active');
            }
        });
    });
}

// Scroll effects
function initializeScrollEffects() {
    const navbar = document.getElementById('navbar');
    
    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
    
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.feature-card, .step, .gallery-item, .pricing-card');
    animateElements.forEach(el => {
        observer.observe(el);
    });
}

// Enhanced smooth scrolling for hero CTA and all internal links
document.addEventListener('DOMContentLoaded', function() {
    // Hero CTA
    const heroCTA = document.querySelector('.hero-cta');
    if (heroCTA) {
        heroCTA.addEventListener('click', function(e) {
            e.preventDefault();
            const appSection = document.getElementById('app');
            if (appSection) {
                const offsetTop = appSection.offsetTop - 70;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    }
    
    // All pricing buttons and internal links
    const internalLinks = document.querySelectorAll('a[href^="#"]');
    internalLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 70;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
});

// Pricing card interactions
document.addEventListener('DOMContentLoaded', function() {
    const pricingCards = document.querySelectorAll('.pricing-card');
    
    pricingCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-15px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            if (this.classList.contains('pricing-featured')) {
                this.style.transform = 'scale(1.05)';
            } else {
                this.style.transform = 'translateY(0) scale(1)';
            }
        });
    });
});

// Gallery lightbox functionality
document.addEventListener('DOMContentLoaded', function() {
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    galleryItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const img = this.querySelector('img');
            if (img) {
                openImageModal(img.src);
            }
        });
    });
});

// Enhanced loading animation
function enhancedLoadingAnimation() {
    const loadingTexts = [
        'Analyzing your beautiful photo...',
        'Selecting the perfect dress style...',
        'Preparing the scenic backdrop...',
        'Adjusting lighting and composition...',
        'Adding magical AI touches...',
        'Finalizing your stunning portrait...'
    ];
    
    let currentStep = 0;
    const interval = setInterval(() => {
        if (currentStep < loadingTexts.length) {
            loadingText.textContent = loadingTexts[currentStep];
            progressFill.style.width = `${((currentStep + 1) / loadingTexts.length) * 100}%`;
            currentStep++;
        } else {
            clearInterval(interval);
        }
    }, 1000);
    
    return new Promise(resolve => {
        setTimeout(resolve, loadingTexts.length * 1000);
    });
}

// Update the existing generateWeddingPortrait function to use enhanced loading
async function generateWeddingPortrait() {
    // Check authentication and subscription first
    if (!authManager.isAuthenticated()) {
        authManager.showLoginModal();
        return;
    }
    
    if (!authManager.canUseService()) {
        if (!authManager.hasActiveSubscription()) {
            authManager.showSubscriptionRequiredModal();
        } else {
            authManager.showNoPhotosRemainingModal();
        }
        return;
    }
    
    if (!uploadedFile || !selectedStyle || !selectedScene) {
        showNotification('Please complete all steps before generating.', 'warning');
        return;
    }

    // Get control values
    const lighting = document.getElementById('lighting').value;
    const pose = document.getElementById('pose').value;
    const quality = document.getElementById('quality').value;

    // Show loading section
    hideSection(controlsSection);
    showSection(loadingSection);

    try {
        // Use enhanced loading animation
        await enhancedLoadingAnimation();

        // Use a photo from subscription
        authManager.usePhoto();

        // Generate mock results
        generateMockResults();

        // Show results
        hideSection(loadingSection);
        showSection(resultsSection);
        
        // Show success notification with remaining photos
        const remaining = authManager.getRemainingPhotos();
        if (remaining === -1) {
            showNotification('Wedding portrait generated successfully! You have unlimited photos.', 'success');
        } else if (remaining > 0) {
            showNotification(`Wedding portrait generated successfully! ${remaining} photos remaining.`, 'success');
        } else {
            showNotification('Wedding portrait generated successfully! This was your last photo. Consider upgrading for more.', 'warning');
        }
        
        // Track the generation event
        trackEvent('wedding_portrait_generated', {
            style: selectedStyle,
            scene: selectedScene,
            lighting: lighting,
            pose: pose,
            quality: quality,
            subscription_type: authManager.currentUser.subscriptionType,
            photos_remaining: remaining
        });
    } catch (error) {
        hideSection(loadingSection);
        showSection(controlsSection);
        showNotification('Failed to generate wedding portrait. Please try again.', 'error');
        console.error('Generation error:', error);
    }
}