<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Wedding Dress Generator | Create Stunning Wedding Photos Online | Free Trial</title>
    <meta name="description" content="Transform your photos into professional wedding portraits with <PERSON> in minutes. Choose from 6 elegant dress styles & scenic backdrops. Free trial available. No photographer needed!">
    <meta name="keywords" content="AI wedding dress generator, wedding photo editor, AI wedding portraits, virtual wedding dress try on, wedding photography AI, bridal photo generator, wedding dress simulator, AI photo editing, wedding picture maker, bridal portrait creator">
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta name="author" content="AI Wedding Dress Studio">
    <link rel="canonical" href="https://ai-wedding-dress-studio.com/">
    
    <!-- Additional SEO Meta Tags -->
    <meta name="geo.region" content="US">
    <meta name="geo.placename" content="United States">
    <meta name="language" content="English">
    <meta name="rating" content="General">
    <meta name="distribution" content="Global">
    <meta name="revisit-after" content="7 days">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=600&h=800&fit=crop" as="image">
    
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="auth-ui.css">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Wedding AI">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://ai-wedding-dress-studio.com/">
    <meta property="og:title" content="AI Wedding Dress Generator | Create Stunning Wedding Photos Online">
    <meta property="og:description" content="Transform your photos into professional wedding portraits with AI in minutes. Free trial available. No photographer needed!">
    <meta property="og:image" content="https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=1200&h=630&fit=crop">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="AI Wedding Dress Generator - Beautiful wedding portrait created with AI">
    <meta property="og:site_name" content="AI Wedding Dress Studio">
    <meta property="og:locale" content="en_US">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://ai-wedding-dress-studio.com/">
    <meta property="twitter:title" content="AI Wedding Dress Generator | Create Stunning Wedding Photos Online">
    <meta property="twitter:description" content="Transform your photos into professional wedding portraits with AI in minutes. Free trial available. No photographer needed!">
    <meta property="twitter:image" content="https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=1200&h=630&fit=crop">
    <meta property="twitter:image:alt" content="AI Wedding Dress Generator - Beautiful wedding portrait created with AI">
    <meta property="twitter:creator" content="@AIWeddingStudio">
    <meta property="twitter:site" content="@AIWeddingStudio">
    
    <!-- Structured Data (JSON-LD) -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "AI Wedding Dress Generator",
        "description": "Transform your photos into professional wedding portraits with AI in minutes. Choose from 6 elegant dress styles and scenic backdrops.",
        "url": "https://ai-wedding-dress-studio.com",
        "applicationCategory": "PhotoEditingApplication",
        "operatingSystem": "Web Browser",
        "offers": [
            {
                "@type": "Offer",
                "name": "Free Trial",
                "price": "0",
                "priceCurrency": "USD",
                "description": "3 AI-generated wedding photos"
            },
            {
                "@type": "Offer",
                "name": "Pro Package",
                "price": "19",
                "priceCurrency": "USD",
                "description": "20 AI-generated wedding photos with high resolution"
            },
            {
                "@type": "Offer",
                "name": "Wedding Package",
                "price": "49",
                "priceCurrency": "USD",
                "description": "Unlimited AI-generated wedding photos with premium features"
            }
        ],
        "provider": {
            "@type": "Organization",
            "name": "AI Wedding Dress Studio",
            "url": "https://ai-wedding-dress-studio.com",
            "logo": "https://ai-wedding-dress-studio.com/logo.png",
            "sameAs": [
                "https://facebook.com/aiweddingstudio",
                "https://twitter.com/aiweddingstudio",
                "https://instagram.com/aiweddingstudio"
            ]
        },
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.9",
            "reviewCount": "1247",
            "bestRating": "5",
            "worstRating": "1"
        },
        "featureList": [
            "AI-powered wedding dress generation",
            "6 elegant dress styles",
            "6 scenic wedding backdrops",
            "High-resolution downloads",
            "Mobile-friendly interface",
            "Instant results in minutes"
        ]
    }
    </script>
    
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": [
            {
                "@type": "Question",
                "name": "How does the AI wedding dress generator work?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Our AI uses advanced machine learning algorithms to analyze your uploaded photo and seamlessly blend it with your chosen wedding dress style and scenic backdrop. The process takes into account lighting, pose, and facial features to create photorealistic results."
                }
            },
            {
                "@type": "Question",
                "name": "What photo quality do I need for best results?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "For optimal results, upload a clear, front-facing photo with good lighting. The photo should be at least 512x512 pixels, and your face should be clearly visible. Avoid heavily filtered or low-resolution images."
                }
            },
            {
                "@type": "Question",
                "name": "How long does it take to generate a wedding portrait?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Most portraits are generated within 2-5 minutes. The exact time depends on the complexity of your selections and current server load. You'll see a progress bar during generation."
                }
            },
            {
                "@type": "Question",
                "name": "Can I use the generated photos commercially?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Commercial use is included with our Pro and Wedding packages. Free trial photos are for personal use only. All commercial licenses include full rights to use the images for business purposes."
                }
            }
        ]
    }
    </script>
    
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://ai-wedding-dress-studio.com/"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Features",
                "item": "https://ai-wedding-dress-studio.com/#features"
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": "Pricing",
                "item": "https://ai-wedding-dress-studio.com/#pricing"
            },
            {
                "@type": "ListItem",
                "position": 4,
                "name": "FAQ",
                "item": "https://ai-wedding-dress-studio.com/#faq"
            }
        ]
    }
    </script>
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-magic"></i>
                <span>AI Wedding Studio</span>
            </div>
            <ul class="nav-menu" id="navMenu">
                <li class="nav-item">
                    <a href="#home" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="#features" class="nav-link">Features</a>
                </li>
                <li class="nav-item">
                    <a href="#how-it-works" class="nav-link">How It Works</a>
                </li>
                <li class="nav-item">
                    <a href="#gallery" class="nav-link">Gallery</a>
                </li>
                <li class="nav-item">
                    <a href="#pricing" class="nav-link">Pricing</a>
                </li>
                <li class="nav-item">
                    <a href="#faq" class="nav-link">FAQ</a>
                </li>
                <li class="nav-item">
                    <a href="#app" class="nav-link nav-cta auth-trigger" data-action="generate">Try Now</a>
                </li>
            </ul>
            
            <!-- Authentication Elements -->
            <div class="auth-element">
                <a href="#" class="auth-btn" onclick="authManager.showLoginModal(); return false;">Login</a>
                <a href="#" class="auth-btn primary" onclick="authManager.showRegisterModal(); return false;">Sign Up</a>
            </div>
            
            <!-- User Elements (hidden by default) -->
            <div class="user-element">
                <div id="subscriptionStatus" class="subscription-status"></div>
                <div class="user-menu">
                    <div class="user-avatar" onclick="toggleUserDropdown()">
                        <span class="user-name-initial"></span>
                    </div>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" onclick="showAccountModal(); toggleUserDropdown();">
                            <i class="fas fa-user"></i> Account
                        </a>
                        <a href="#" onclick="showSubscriptionModal(); toggleUserDropdown();">
                            <i class="fas fa-crown"></i> Subscription
                        </a>
                        <a href="#" onclick="authManager.logout(); toggleUserDropdown();">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
            <div class="nav-toggle" id="navToggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">
                    AI Wedding Dress Generator - Transform Your Photos into 
                    <span class="gradient-text">Professional Wedding Portraits</span>
                </h1>
                <p class="hero-subtitle">
                    Create stunning wedding photos with AI technology in minutes. Choose from 6 elegant wedding dress styles and 6 beautiful scenic backdrops. 
                    Get professional-quality wedding portraits without expensive photography sessions. Free trial available!
                </p>
                <div class="hero-buttons">
                    <a href="#app" class="btn-primary hero-cta auth-trigger" data-action="generate">
                        <i class="fas fa-magic"></i>
                        Start Creating Now
                    </a>
                    <a href="#how-it-works" class="btn-secondary">
                        <i class="fas fa-play"></i>
                        See How It Works
                    </a>
                </div>
                <div class="hero-stats">
                    <div class="stat">
                        <span class="stat-number">10,000+</span>
                        <span class="stat-label">Photos Created</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">6</span>
                        <span class="stat-label">Dress Styles</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">6</span>
                        <span class="stat-label">Scenic Backdrops</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">4.9★</span>
                        <span class="stat-label">User Rating</span>
                    </div>
                </div>
            </div>
            <div class="hero-visual">
                <div class="hero-image-container">
                    <img src="https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=600&h=800&fit=crop" alt="Beautiful AI-generated wedding portrait" class="hero-image">
                    <div class="hero-image-overlay">
                        <div class="floating-card">
                            <i class="fas fa-magic"></i>
                            <span>AI Powered</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="hero-background">
            <div class="hero-shape hero-shape-1"></div>
            <div class="hero-shape hero-shape-2"></div>
            <div class="hero-shape hero-shape-3"></div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Why Choose Our AI Wedding Dress Generator?</h2>
                <p class="section-subtitle">Professional wedding photography made accessible with cutting-edge AI technology. Create beautiful wedding portraits without hiring expensive photographers.</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h3 class="feature-title">AI-Powered Generation</h3>
                    <p class="feature-description">Advanced AI algorithms create photorealistic wedding portraits that look professionally shot</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-tshirt"></i>
                    </div>
                    <h3 class="feature-title">6 Dress Styles</h3>
                    <p class="feature-description">From classic elegance to bohemian dreams - find the perfect dress style for your vision</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mountain"></i>
                    </div>
                    <h3 class="feature-title">Beautiful Scenes</h3>
                    <p class="feature-description">Choose from 6 stunning backdrops: church, beach, garden, castle, vineyard, and ballroom</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="feature-title">Minutes, Not Hours</h3>
                    <p class="feature-description">Get professional results in under 5 minutes - no lengthy photo shoots required</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">Mobile Friendly</h3>
                    <p class="feature-description">Works perfectly on any device - desktop, tablet, or smartphone</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <h3 class="feature-title">High-Quality Downloads</h3>
                    <p class="feature-description">Download your photos in ultra-high resolution perfect for printing and sharing</p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="how-it-works" id="how-it-works">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">How Our AI Wedding Dress Generator Works</h2>
                <p class="section-subtitle">Create your dream wedding photos with AI in 4 simple steps. No photography experience required!</p>
            </div>
            <div class="steps-container">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3 class="step-title">Upload Your Photo</h3>
                        <p class="step-description">Upload a clear photo of yourself. Our AI works best with front-facing portraits with good lighting.</p>
                        <div class="step-visual">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3 class="step-title">Choose Your Style</h3>
                        <p class="step-description">Select from 6 beautiful wedding dress styles - from classic elegance to modern chic.</p>
                        <div class="step-visual">
                            <i class="fas fa-tshirt"></i>
                        </div>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3 class="step-title">Pick Your Scene</h3>
                        <p class="step-description">Choose the perfect backdrop for your wedding portrait from our collection of stunning venues.</p>
                        <div class="step-visual">
                            <i class="fas fa-mountain"></i>
                        </div>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3 class="step-title">Download & Share</h3>
                        <p class="step-description">Get your professional wedding portraits in minutes. Download in high resolution and share with loved ones.</p>
                        <div class="step-visual">
                            <i class="fas fa-download"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section class="gallery" id="gallery">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">AI-Generated Wedding Portrait Gallery</h2>
                <p class="section-subtitle">See the stunning wedding portraits our AI wedding dress generator creates. Real examples from satisfied customers.</p>
            </div>
            <div class="gallery-grid">
                <div class="gallery-item">
                    <img src="https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&h=600&fit=crop" alt="AI-generated classic elegance wedding dress portrait in church setting" loading="lazy">
                    <div class="gallery-overlay">
                        <h4>Classic Elegance</h4>
                        <p>Church Setting</p>
                    </div>
                </div>
                <div class="gallery-item">
                    <img src="https://images.unsplash.com/photo-1515934751635-c81c6bc9a2d8?w=400&h=600&fit=crop" alt="AI-generated modern chic wedding dress portrait in ballroom setting" loading="lazy">
                    <div class="gallery-overlay">
                        <h4>Modern Chic</h4>
                        <p>Ballroom Setting</p>
                    </div>
                </div>
                <div class="gallery-item">
                    <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=400&h=600&fit=crop" alt="AI-generated vintage romance wedding dress portrait in garden setting" loading="lazy">
                    <div class="gallery-overlay">
                        <h4>Vintage Romance</h4>
                        <p>Garden Setting</p>
                    </div>
                </div>
                <div class="gallery-item">
                    <img src="https://images.unsplash.com/photo-1566174053879-31528523f8ae?w=400&h=600&fit=crop" alt="AI-generated bohemian dream wedding dress portrait in beach setting" loading="lazy">
                    <div class="gallery-overlay">
                        <h4>Bohemian Dream</h4>
                        <p>Beach Setting</p>
                    </div>
                </div>
                <div class="gallery-item">
                    <img src="https://images.unsplash.com/photo-1583939003579-730e3918a45a?w=400&h=600&fit=crop" alt="AI-generated princess ball gown wedding dress portrait in castle setting" loading="lazy">
                    <div class="gallery-overlay">
                        <h4>Princess Ball Gown</h4>
                        <p>Castle Setting</p>
                    </div>
                </div>
                <div class="gallery-item">
                    <img src="https://images.unsplash.com/photo-1595476108010-b4d1f102b1b1?w=400&h=600&fit=crop" alt="AI-generated minimalist beauty wedding dress portrait in vineyard setting" loading="lazy">
                    <div class="gallery-overlay">
                        <h4>Minimalist Beauty</h4>
                        <p>Vineyard Setting</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing" id="pricing">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">AI Wedding Dress Generator Pricing</h2>
                <p class="section-subtitle">Affordable pricing for professional wedding portraits. Choose the plan that works best for your needs. Start with our free trial!</p>
            </div>
            <div class="pricing-grid">
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3 class="pricing-title">Free Trial</h3>
                        <div class="pricing-price">
                            <span class="price">$0</span>
                            <span class="period">/ 3 photos</span>
                        </div>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> 3 AI-generated photos</li>
                        <li><i class="fas fa-check"></i> All dress styles</li>
                        <li><i class="fas fa-check"></i> All scene options</li>
                        <li><i class="fas fa-check"></i> Standard quality</li>
                        <li><i class="fas fa-times"></i> High-resolution downloads</li>
                        <li><i class="fas fa-times"></i> Commercial use</li>
                    </ul>
                    <a href="#" class="pricing-btn btn-secondary auth-trigger" data-plan="free_trial">Start Free Trial</a>
                </div>
                <div class="pricing-card pricing-featured">
                    <div class="pricing-badge">Most Popular</div>
                    <div class="pricing-header">
                        <h3 class="pricing-title">Pro Package</h3>
                        <div class="pricing-price">
                            <span class="price">$19</span>
                            <span class="period">/ month</span>
                        </div>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> 20 AI-generated photos</li>
                        <li><i class="fas fa-check"></i> All dress styles</li>
                        <li><i class="fas fa-check"></i> All scene options</li>
                        <li><i class="fas fa-check"></i> Ultra-high quality</li>
                        <li><i class="fas fa-check"></i> High-resolution downloads</li>
                        <li><i class="fas fa-check"></i> Commercial use license</li>
                    </ul>
                    <a href="#" class="pricing-btn btn-primary auth-trigger" data-plan="pro">Get Pro Package</a>
                </div>
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3 class="pricing-title">Wedding Package</h3>
                        <div class="pricing-price">
                            <span class="price">$49</span>
                            <span class="period">/ month</span>
                        </div>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> Unlimited photos</li>
                        <li><i class="fas fa-check"></i> All dress styles</li>
                        <li><i class="fas fa-check"></i> All scene options</li>
                        <li><i class="fas fa-check"></i> Ultra-high quality</li>
                        <li><i class="fas fa-check"></i> High-resolution downloads</li>
                        <li><i class="fas fa-check"></i> Commercial use license</li>
                        <li><i class="fas fa-check"></i> Priority support</li>
                        <li><i class="fas fa-check"></i> Custom requests</li>
                    </ul>
                    <a href="#" class="pricing-btn btn-secondary auth-trigger" data-plan="wedding">Get Wedding Package</a>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq" id="faq">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">AI Wedding Dress Generator FAQ</h2>
                <p class="section-subtitle">Everything you need to know about our AI wedding portrait generator. Get answers to common questions about creating wedding photos with AI.</p>
            </div>
            <div class="faq-container">
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How does the AI wedding dress generator work?</h3>
                        <i class="fas fa-plus faq-icon"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Our AI uses advanced machine learning algorithms to analyze your uploaded photo and seamlessly blend it with your chosen wedding dress style and scenic backdrop. The process takes into account lighting, pose, and facial features to create photorealistic results.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What photo quality do I need for best results?</h3>
                        <i class="fas fa-plus faq-icon"></i>
                    </div>
                    <div class="faq-answer">
                        <p>For optimal results, upload a clear, front-facing photo with good lighting. The photo should be at least 512x512 pixels, and your face should be clearly visible. Avoid heavily filtered or low-resolution images.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How long does it take to generate a wedding portrait?</h3>
                        <i class="fas fa-plus faq-icon"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Most portraits are generated within 2-5 minutes. The exact time depends on the complexity of your selections and current server load. You'll see a progress bar during generation.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>Can I use the generated photos commercially?</h3>
                        <i class="fas fa-plus faq-icon"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Commercial use is included with our Pro and Wedding packages. Free trial photos are for personal use only. All commercial licenses include full rights to use the images for business purposes.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What file formats and resolutions are available?</h3>
                        <i class="fas fa-plus faq-icon"></i>
                    </div>
                    <div class="faq-answer">
                        <p>All photos are delivered in high-quality JPEG format. Pro and Wedding packages include ultra-high resolution (up to 4K) suitable for large prints. Free trial includes standard web resolution.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>Is my uploaded photo stored or shared?</h3>
                        <i class="fas fa-plus faq-icon"></i>
                    </div>
                    <div class="faq-answer">
                        <p>We take privacy seriously. Your uploaded photos are processed securely and automatically deleted from our servers after 24 hours. We never share or store your personal images beyond the processing period.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>Can I request custom dress styles or scenes?</h3>
                        <i class="fas fa-plus faq-icon"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Custom requests are available with our Wedding Package. Contact our support team with your specific requirements, and we'll work to accommodate special dress styles or unique venue requests.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What if I'm not satisfied with the results?</h3>
                        <i class="fas fa-plus faq-icon"></i>
                    </div>
                    <div class="faq-answer">
                        <p>We offer a 100% satisfaction guarantee. If you're not happy with your results, contact us within 7 days for a full refund or free regeneration with different parameters.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- App Section -->
    <section class="app-section" id="app">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Create Your Wedding Portrait</h2>
                <p class="section-subtitle">Upload your photo and let our AI create stunning wedding portraits</p>
            </div>
        </div>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Upload Section -->
            <section class="upload-section" id="uploadSection">
                <div class="upload-card">
                    <h2>Upload Your Photo</h2>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-placeholder">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>Drag & drop your photo here or click to browse</p>
                            <small>Supports JPG, PNG, WEBP (Max 10MB)</small>
                        </div>
                        <input type="file" id="photoInput" accept="image/*" hidden>
                        <div class="uploaded-image" id="uploadedImage" style="display: none;">
                            <img id="previewImage" src="" alt="Uploaded photo">
                            <button class="remove-btn" id="removeImage">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Style Selection -->
            <section class="style-section" id="styleSection" style="display: none;">
                <div class="style-card">
                    <h2>Choose Wedding Dress Style</h2>
                    <div class="style-grid">
                        <div class="style-option" data-style="classic">
                            <img src="https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300&h=400&fit=crop" alt="Classic Wedding Dress">
                            <div class="style-overlay">
                                <h3>Classic Elegance</h3>
                                <p>Timeless A-line with lace details</p>
                            </div>
                        </div>
                        <div class="style-option" data-style="modern">
                            <img src="https://images.unsplash.com/photo-1515934751635-c81c6bc9a2d8?w=300&h=400&fit=crop" alt="Modern Wedding Dress">
                            <div class="style-overlay">
                                <h3>Modern Chic</h3>
                                <p>Sleek silhouette with contemporary design</p>
                            </div>
                        </div>
                        <div class="style-option" data-style="vintage">
                            <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=300&h=400&fit=crop" alt="Vintage Wedding Dress">
                            <div class="style-overlay">
                                <h3>Vintage Romance</h3>
                                <p>Retro-inspired with intricate beading</p>
                            </div>
                        </div>
                        <div class="style-option" data-style="bohemian">
                            <img src="https://images.unsplash.com/photo-1566174053879-31528523f8ae?w=300&h=400&fit=crop" alt="Bohemian Wedding Dress">
                            <div class="style-overlay">
                                <h3>Bohemian Dream</h3>
                                <p>Free-spirited with flowing fabrics</p>
                            </div>
                        </div>
                        <div class="style-option" data-style="princess">
                            <img src="https://images.unsplash.com/photo-1583939003579-730e3918a45a?w=300&h=400&fit=crop" alt="Princess Wedding Dress">
                            <div class="style-overlay">
                                <h3>Princess Ball Gown</h3>
                                <p>Dramatic volume with royal elegance</p>
                            </div>
                        </div>
                        <div class="style-option" data-style="minimalist">
                            <img src="https://images.unsplash.com/photo-1595476108010-b4d1f102b1b1?w=300&h=400&fit=crop" alt="Minimalist Wedding Dress">
                            <div class="style-overlay">
                                <h3>Minimalist Beauty</h3>
                                <p>Clean lines and simple sophistication</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Scene Selection -->
            <section class="scene-section" id="sceneSection" style="display: none;">
                <div class="scene-card">
                    <h2>Choose Wedding Scene</h2>
                    <div class="scene-grid">
                        <div class="scene-option" data-scene="church">
                            <img src="https://images.unsplash.com/photo-1519741497674-611481863552?w=400&h=300&fit=crop" alt="Church Wedding">
                            <div class="scene-overlay">
                                <h3>Classic Church</h3>
                                <p>Traditional ceremony setting</p>
                            </div>
                        </div>
                        <div class="scene-option" data-scene="beach">
                            <img src="https://images.unsplash.com/photo-1511285560929-80b456fea0bc?w=400&h=300&fit=crop" alt="Beach Wedding">
                            <div class="scene-overlay">
                                <h3>Beach Paradise</h3>
                                <p>Romantic seaside ceremony</p>
                            </div>
                        </div>
                        <div class="scene-option" data-scene="garden">
                            <img src="https://images.unsplash.com/photo-1465495976277-4387d4b0e4a6?w=400&h=300&fit=crop" alt="Garden Wedding">
                            <div class="scene-overlay">
                                <h3>Garden Romance</h3>
                                <p>Lush botanical backdrop</p>
                            </div>
                        </div>
                        <div class="scene-option" data-scene="castle">
                            <img src="https://images.unsplash.com/photo-1520637836862-4d197d17c93a?w=400&h=300&fit=crop" alt="Castle Wedding">
                            <div class="scene-overlay">
                                <h3>Fairytale Castle</h3>
                                <p>Majestic royal setting</p>
                            </div>
                        </div>
                        <div class="scene-option" data-scene="vineyard">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop" alt="Vineyard Wedding">
                            <div class="scene-overlay">
                                <h3>Vineyard Elegance</h3>
                                <p>Rustic countryside charm</p>
                            </div>
                        </div>
                        <div class="scene-option" data-scene="ballroom">
                            <img src="https://images.unsplash.com/photo-1519167758481-83f29c8e8d4b?w=400&h=300&fit=crop" alt="Ballroom Wedding">
                            <div class="scene-overlay">
                                <h3>Grand Ballroom</h3>
                                <p>Luxurious indoor celebration</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Generation Controls -->
            <section class="controls-section" id="controlsSection" style="display: none;">
                <div class="controls-card">
                    <h2>Customize Your Portrait</h2>
                    <div class="controls-grid">
                        <div class="control-group">
                            <label for="lighting">Lighting Style</label>
                            <select id="lighting">
                                <option value="natural">Natural Light</option>
                                <option value="golden">Golden Hour</option>
                                <option value="dramatic">Dramatic</option>
                                <option value="soft">Soft & Romantic</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label for="pose">Pose Style</label>
                            <select id="pose">
                                <option value="portrait">Classic Portrait</option>
                                <option value="candid">Candid Moment</option>
                                <option value="walking">Walking Shot</option>
                                <option value="sitting">Seated Elegance</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label for="quality">Image Quality</label>
                            <select id="quality">
                                <option value="standard">Standard</option>
                                <option value="high">High Quality</option>
                                <option value="ultra">Ultra HD</option>
                            </select>
                        </div>
                    </div>
                    <button class="generate-btn" id="generateBtn">
                        <i class="fas fa-magic"></i>
                        Generate Wedding Portrait
                    </button>
                </div>
            </section>

            <!-- Loading Section -->
            <section class="loading-section" id="loadingSection" style="display: none;">
                <div class="loading-card">
                    <div class="loading-animation">
                        <div class="loading-spinner"></div>
                        <h3>Creating Your Wedding Portrait...</h3>
                        <p id="loadingText">Analyzing your photo...</p>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="results-card">
                    <h2>Your Wedding Portraits</h2>
                    <div class="results-grid" id="resultsGrid">
                        <!-- Generated images will be inserted here -->
                    </div>
                    <div class="results-actions">
                        <button class="btn-secondary" id="generateMoreBtn">
                            <i class="fas fa-plus"></i>
                            Generate More Variations
                        </button>
                        <button class="btn-primary" id="downloadAllBtn">
                            <i class="fas fa-download"></i>
                            Download All
                        </button>
                    </div>
                </div>
            </section>
        </main>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-magic"></i>
                        <span>AI Wedding Studio</span>
                    </div>
                    <p class="footer-description">
                        Transform your photos into stunning wedding portraits with cutting-edge AI technology. 
                        Professional results in minutes.
                    </p>
                    <div class="footer-social">
                        <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-pinterest"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Product</h4>
                    <ul class="footer-links">
                        <li><a href="#features">Features</a></li>
                        <li><a href="#pricing">Pricing</a></li>
                        <li><a href="#gallery">Gallery</a></li>
                        <li><a href="#app">Try Now</a></li>
                        <li><a href="#faq">FAQ</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Company</h4>
                    <ul class="footer-links">
                        <li><a href="#about">About Us</a></li>
                        <li><a href="#careers">Careers</a></li>
                        <li><a href="#press">Press</a></li>
                        <li><a href="#blog">Blog</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Support</h4>
                    <ul class="footer-links">
                        <li><a href="#help">Help Center</a></li>
                        <li><a href="#tutorials">Tutorials</a></li>
                        <li><a href="#community">Community</a></li>
                        <li><a href="#status">Status</a></li>
                        <li><a href="#feedback">Feedback</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Legal</h4>
                    <ul class="footer-links">
                        <li><a href="#privacy">Privacy Policy</a></li>
                        <li><a href="#terms">Terms of Service</a></li>
                        <li><a href="#cookies">Cookie Policy</a></li>
                        <li><a href="#licenses">Licenses</a></li>
                        <li><a href="#dmca">DMCA</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <p>&copy; 2024 AI Wedding Dress Studio. All rights reserved. Made with ❤️ for your special day.</p>
                    <div class="footer-bottom-links">
                        <a href="#privacy">Privacy</a>
                        <a href="#terms">Terms</a>
                        <a href="#cookies">Cookies</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Modal for Image Preview -->
    <div class="modal" id="imageModal" style="display: none;">
        <div class="modal-content">
            <span class="close-modal" id="closeModal">&times;</span>
            <img id="modalImage" src="" alt="Generated Portrait">
            <div class="modal-actions">
                <button class="btn-primary" id="downloadSingle">
                    <i class="fas fa-download"></i>
                    Download
                </button>
                <button class="btn-secondary" id="shareImage">
                    <i class="fas fa-share"></i>
                    Share
                </button>
            </div>
        </div>
    </div>

    <!-- Authentication Modals -->
    <div id="loginModal" class="modal-overlay" style="display: none;">
        <div class="auth-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Welcome Back</h2>
                    <span class="close-modal" onclick="authManager.closeModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="auth-tabs">
                        <button class="auth-tab active" onclick="switchAuthTab('login')">Login</button>
                        <button class="auth-tab" onclick="switchAuthTab('register')">Sign Up</button>
                    </div>
                    
                    <!-- Login Form -->
                    <form class="auth-form active" id="loginForm">
                        <div class="form-group">
                            <label for="loginEmail">Email Address</label>
                            <input type="email" id="loginEmail" required placeholder="Enter your email">
                        </div>
                        <div class="form-group">
                            <label for="loginPassword">Password</label>
                            <input type="password" id="loginPassword" required placeholder="Enter your password">
                        </div>
                        <button type="submit" class="auth-submit">Login</button>
                        <div class="auth-links">
                            <a href="#" onclick="showForgotPasswordModal()">Forgot Password?</a>
                        </div>
                    </form>
                    
                    <!-- Register Form -->
                    <form class="auth-form" id="registerForm">
                        <div class="form-group">
                            <label for="registerName">Full Name</label>
                            <input type="text" id="registerName" required placeholder="Enter your full name">
                        </div>
                        <div class="form-group">
                            <label for="registerEmail">Email Address</label>
                            <input type="email" id="registerEmail" required placeholder="Enter your email">
                        </div>
                        <div class="form-group">
                            <label for="registerPassword">Password</label>
                            <input type="password" id="registerPassword" required placeholder="Create a password">
                        </div>
                        <div class="form-group">
                            <label for="confirmPassword">Confirm Password</label>
                            <input type="password" id="confirmPassword" required placeholder="Confirm your password">
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="agreeTerms" required>
                                I agree to the <a href="#terms">Terms of Service</a> and <a href="#privacy">Privacy Policy</a>
                            </label>
                        </div>
                        <button type="submit" class="auth-submit">Start Free Trial</button>
                        <p class="trial-notice">
                            🎉 Get 3 days free trial with 3 AI-generated wedding photos!
                        </p>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="auth.js"></script>
    <script src="script.js"></script>
    
    <script>
        // Authentication form handlers
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            const submitBtn = e.target.querySelector('.auth-submit');
            submitBtn.textContent = 'Logging in...';
            submitBtn.disabled = true;
            
            try {
                const result = await authManager.login(email, password);
                if (result.success) {
                    authManager.closeModal();
                } else {
                    alert('Login failed: ' + result.error);
                }
            } catch (error) {
                alert('Login error: ' + error.message);
            } finally {
                submitBtn.textContent = 'Login';
                submitBtn.disabled = false;
            }
        });
        
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const name = document.getElementById('registerName').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (password !== confirmPassword) {
                alert('Passwords do not match');
                return;
            }
            
            const submitBtn = e.target.querySelector('.auth-submit');
            submitBtn.textContent = 'Creating Account...';
            submitBtn.disabled = true;
            
            try {
                const result = await authManager.register(email, password, name);
                if (result.success) {
                    authManager.closeModal();
                } else {
                    alert('Registration failed: ' + result.error);
                }
            } catch (error) {
                alert('Registration error: ' + error.message);
            } finally {
                submitBtn.textContent = 'Start Free Trial';
                submitBtn.disabled = false;
            }
        });
        
        // Auth tab switching
        function switchAuthTab(tab) {
            const tabs = document.querySelectorAll('.auth-tab');
            const forms = document.querySelectorAll('.auth-form');
            
            tabs.forEach(t => t.classList.remove('active'));
            forms.forEach(f => f.classList.remove('active'));
            
            document.querySelector(`[onclick="switchAuthTab('${tab}')"]`).classList.add('active');
            document.getElementById(tab + 'Form').classList.add('active');
        }
        
        // User dropdown toggle
        function toggleUserDropdown() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');
        }
        
        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.user-menu')) {
                document.getElementById('userDropdown').classList.remove('show');
            }
        });
        
        // Show account modal
        function showAccountModal() {
            const user = authManager.currentUser;
            if (!user) return;
            
            const modal = authManager.createModal('Account Settings', `
                <div class="account-settings">
                    <div class="account-info">
                        <h3>Account Information</h3>
                        <div class="info-row">
                            <span class="label">Name:</span>
                            <span class="value">${user.name}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">Email:</span>
                            <span class="value">${user.email}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">Member Since:</span>
                            <span class="value">${new Date(user.createdAt).toLocaleDateString()}</span>
                        </div>
                    </div>
                    <div class="account-actions">
                        <button class="btn-secondary" onclick="authManager.closeModal()">Close</button>
                    </div>
                </div>
            `);
            authManager.showModal(modal);
        }
        
        // Show subscription modal
        function showSubscriptionModal() {
            const user = authManager.currentUser;
            if (!user) return;
            
            const plan = authManager.subscriptionPlans[user.subscriptionType];
            const modal = authManager.createModal('Subscription Details', `
                <div class="subscription-details-modal">
                    <div class="current-plan">
                        <h3>Current Plan: ${plan ? plan.name : 'No Active Plan'}</h3>
                        ${plan ? `
                            <div class="plan-info">
                                <div class="info-row">
                                    <span class="label">Status:</span>
                                    <span class="value ${authManager.hasActiveSubscription() ? 'active' : 'expired'}">
                                        ${authManager.hasActiveSubscription() ? 'Active' : 'Expired'}
                                    </span>
                                </div>
                                <div class="info-row">
                                    <span class="label">Photos Used:</span>
                                    <span class="value">${user.photosUsed} / ${plan.photos === -1 ? 'Unlimited' : plan.photos}</span>
                                </div>
                                <div class="info-row">
                                    <span class="label">Expires:</span>
                                    <span class="value">${new Date(user.subscriptionEnd).toLocaleDateString()}</span>
                                </div>
                            </div>
                        ` : '<p>No active subscription. Choose a plan to start creating!</p>'}
                    </div>
                    <div class="subscription-actions">
                        ${!authManager.hasActiveSubscription() ? `
                            <button class="btn-primary subscribe-btn" data-plan="pro">Subscribe to Pro</button>
                            <button class="btn-primary subscribe-btn" data-plan="wedding">Subscribe to Wedding</button>
                        ` : ''}
                        <button class="btn-secondary" onclick="authManager.closeModal()">Close</button>
                    </div>
                </div>
            `);
            authManager.showModal(modal);
        }
        
        // Add auth manager methods for showing modals
        authManager.showLoginModal = function() {
            document.getElementById('loginModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        };
        
        authManager.showRegisterModal = function() {
            document.getElementById('loginModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
            switchAuthTab('register');
        };
        
        // Update user avatar initial
        authManager.updateUserAvatar = function() {
            if (this.currentUser) {
                const initial = this.currentUser.name ? this.currentUser.name.charAt(0).toUpperCase() : 
                               this.currentUser.email.charAt(0).toUpperCase();
                document.querySelector('.user-name-initial').textContent = initial;
            }
        };
        
        // Override updateUI to include avatar update
        const originalUpdateUI = authManager.updateUI;
        authManager.updateUI = function() {
            originalUpdateUI.call(this);
            this.updateUserAvatar();
        };
    </script>
</body>
</html>