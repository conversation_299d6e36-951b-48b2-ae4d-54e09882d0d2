import type { Config } from "tailwindcss";

export default {
    content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
  	extend: {
		maxWidth: {
			container: "1280px",
		},
  		colors: {
  			// Light theme colors only
  			background: '#ffffff',
  			foreground: '#1a1a1a',
  			card: {
  				DEFAULT: '#ffffff',
  				foreground: '#1a1a1a'
  			},
  			popover: {
  				DEFAULT: '#ffffff',
  				foreground: '#1a1a1a'
  			},
  			primary: {
  				DEFAULT: '#ec4899',
  				foreground: '#ffffff'
  			},
  			secondary: {
  				DEFAULT: '#f3f4f6',
  				foreground: '#1a1a1a'
  			},
  			muted: {
  				DEFAULT: '#f3f4f6',
  				foreground: '#6b7280'
  			},
  			accent: {
  				DEFAULT: '#f3f4f6',
  				foreground: '#1a1a1a'
  			},
  			destructive: {
  				DEFAULT: '#ef4444',
  				foreground: '#ffffff'
  			},
  			border: '#e5e7eb',
  			input: '#e5e7eb',
  			ring: '#ec4899'
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		keyframes: {
			marquee: {
				from: { transform: 'translateX(0)' },
				to: { transform: 'translateX(calc(-100% - var(--gap)))' }
			},
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out',
			marquee: 'marquee var(--duration) linear infinite',
		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;
